#!/bin/bash

# 简单的RMAN语法测试脚本
# 功能：验证修复后的RMAN命令是否能正常执行

# 加载环境配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/0_oracle_env.sh"

# 测试日志
TEST_LOG="$BACKUP_LOG_DIR/simple_rman_test_$(date +%Y%m%d_%H%M%S).log"
mkdir -p "$BACKUP_LOG_DIR"

echo "========================================="
echo "Oracle RMAN 简单语法测试"
echo "测试时间: $(date)"
echo "测试日志: $TEST_LOG"
echo "========================================="

# 测试1: 基本连接和配置
echo "测试1: RMAN基本连接和配置..."
rman target / log="$TEST_LOG" <<EOF
SHOW ALL;
EXIT;
EOF

if [ $? -eq 0 ]; then
    echo "✅ 测试1通过: RMAN基本连接正常"
else
    echo "❌ 测试1失败: RMAN连接或配置有问题"
    exit 1
fi

# 测试2: 简单的备份命令语法
echo "测试2: 简单备份命令语法..."
TEST_BACKUP_DIR="$BACKUP_BASE_DIR/syntax_test_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$TEST_BACKUP_DIR"

rman target / log="$TEST_LOG" <<EOF
BACKUP CURRENT CONTROLFILE FORMAT '$TEST_BACKUP_DIR/test_ctl_%U.bkp';
LIST BACKUP SUMMARY;
EXIT;
EOF

if [ $? -eq 0 ]; then
    echo "✅ 测试2通过: 简单备份命令语法正确"
else
    echo "❌ 测试2失败: 备份命令语法有问题"
    exit 1
fi

# 测试3: 增量备份语法（不实际执行，只检查语法）
echo "测试3: 增量备份语法检查..."
rman target / log="$TEST_LOG" <<EOF
# 只检查语法，不实际执行
BACKUP VALIDATE INCREMENTAL LEVEL 0 DATABASE;
EXIT;
EOF

if [ $? -eq 0 ]; then
    echo "✅ 测试3通过: 增量备份语法正确"
else
    echo "❌ 测试3失败: 增量备份语法有问题"
    exit 1
fi

# 测试4: 归档日志备份语法
echo "测试4: 归档日志备份语法..."
rman target / log="$TEST_LOG" <<EOF
# 检查归档日志备份语法
BACKUP VALIDATE ARCHIVELOG ALL;
EXIT;
EOF

if [ $? -eq 0 ]; then
    echo "✅ 测试4通过: 归档日志备份语法正确"
else
    echo "❌ 测试4失败: 归档日志备份语法有问题"
    exit 1
fi

echo "========================================="
echo "🎉 所有RMAN语法测试通过！"
echo "现在可以安全执行完整的备份脚本："
echo "  ./2_rman_backup_level0.sh"
echo "  ./3_rman_backup_level1.sh"
echo "详细日志: $TEST_LOG"
echo "========================================="
