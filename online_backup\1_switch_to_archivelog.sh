#!/bin/bash

# Oracle数据库归档模式切换脚本 - Shell包装器
# 功能：安全地将数据库切换到归档模式，包含完整的检查和日志记录

# 加载环境配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/0_oracle_env.sh"

# 脚本配置
SWITCH_DATE=$(date +%Y%m%d_%H%M%S)
LOGFILE="$BACKUP_LOG_DIR/switch_to_archivelog_$SWITCH_DATE.log"
SQL_SCRIPT="$SCRIPT_DIR/1_switch_to_archivelog.sql"

# 创建日志目录
mkdir -p "$BACKUP_LOG_DIR"

# 日志记录函数
log_to_file() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOGFILE"
}

# 检查前置条件
check_prerequisites() {
    log_to_file "========================================="
    log_to_file "检查归档模式切换前置条件"
    log_to_file "========================================="
    
    # 检查是否为oracle用户
    if [ "$(whoami)" != "oracle" ]; then
        log_to_file "警告: 建议使用oracle用户执行此脚本"
    fi
    
    # 检查SQL脚本是否存在
    if [ ! -f "$SQL_SCRIPT" ]; then
        log_to_file "错误: SQL脚本不存在: $SQL_SCRIPT"
        return 1
    fi
    
    # 检查Oracle环境
    if ! check_oracle_environment; then
        log_to_file "错误: Oracle环境检查失败"
        return 1
    fi
    
    # 检查数据库连接
    if ! sqlplus -s / as sysdba <<< "SELECT 1 FROM DUAL;" > /dev/null 2>&1; then
        log_to_file "错误: 无法连接到数据库"
        return 1
    fi
    
    # 检查当前归档模式
    local current_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT LOG_MODE FROM V\$DATABASE;
EXIT;
EOF
)
    
    if [ "$current_mode" = "ARCHIVELOG" ]; then
        log_to_file "信息: 数据库已经处于归档模式"
        log_to_file "当前模式: $current_mode"
        
        # 询问是否继续
        echo "数据库已经处于归档模式，是否继续执行脚本以查看配置？(y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_to_file "用户选择退出"
            return 1
        fi
    else
        log_to_file "当前归档模式: $current_mode"
        log_to_file "准备切换到归档模式..."
    fi
    
    # 检查磁盘空间
    if ! check_disk_space; then
        log_to_file "警告: 磁盘空间检查失败，但继续执行"
    fi
    
    # 创建FRA目录（单FRA模式）
    if [ ! -d "$FRA_DIR" ]; then
        log_to_file "创建FRA目录: $FRA_DIR"
        mkdir -p "$FRA_DIR"
        if [ $? -ne 0 ]; then
            log_to_file "错误: 无法创建FRA目录"
            return 1
        fi
        # 设置权限
        chown oracle:oinstall "$FRA_DIR" 2>/dev/null || true
        chmod 755 "$FRA_DIR" 2>/dev/null || true
    fi

    # 检查FRA目录权限
    if [ ! -w "$FRA_DIR" ]; then
        log_to_file "错误: FRA目录不可写: $FRA_DIR"
        return 1
    fi
    
    log_to_file "前置条件检查完成"
    return 0
}

# 备份重要文件
backup_important_files() {
    log_to_file "========================================="
    log_to_file "备份重要配置文件"
    log_to_file "========================================="
    
    local backup_dir="$BACKUP_BASE_DIR/config_backup_$SWITCH_DATE"
    mkdir -p "$backup_dir"
    
    # 备份参数文件
    if [ -f "$ORACLE_HOME/dbs/spfile$ORACLE_SID.ora" ]; then
        cp "$ORACLE_HOME/dbs/spfile$ORACLE_SID.ora" "$backup_dir/"
        log_to_file "已备份SPFILE到: $backup_dir/"
    fi
    
    if [ -f "$ORACLE_HOME/dbs/init$ORACLE_SID.ora" ]; then
        cp "$ORACLE_HOME/dbs/init$ORACLE_SID.ora" "$backup_dir/"
        log_to_file "已备份PFILE到: $backup_dir/"
    fi
    
    # 备份控制文件（通过SQL）
    sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
ALTER DATABASE BACKUP CONTROLFILE TO '$backup_dir/control_backup_$SWITCH_DATE.ctl';
ALTER DATABASE BACKUP CONTROLFILE TO TRACE AS '$backup_dir/create_controlfile_$SWITCH_DATE.sql';
EOF
    
    if [ $? -eq 0 ]; then
        log_to_file "控制文件备份完成"
    else
        log_to_file "警告: 控制文件备份可能失败"
    fi
    
    log_to_file "配置文件备份完成，备份位置: $backup_dir"
}

# 执行归档模式切换
execute_archivelog_switch() {
    log_to_file "========================================="
    log_to_file "开始执行归档模式切换"
    log_to_file "========================================="
    
    log_to_file "执行SQL脚本: $SQL_SCRIPT"
    log_to_file "详细日志将记录在: $LOGFILE"
    
    # 执行SQL脚本
    sqlplus / as sysdba @"$SQL_SCRIPT" >> "$LOGFILE" 2>&1
    local sql_exit_code=$?
    
    if [ $sql_exit_code -eq 0 ]; then
        log_to_file "SQL脚本执行完成"
    else
        log_to_file "错误: SQL脚本执行失败，退出代码: $sql_exit_code"
        return 1
    fi
    
    return 0
}

# 验证切换结果
verify_archivelog_mode() {
    log_to_file "========================================="
    log_to_file "验证归档模式切换结果"
    log_to_file "========================================="
    
    # 检查数据库状态
    local db_status=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT STATUS FROM V\$INSTANCE;
EXIT;
EOF
)
    
    if [ "$db_status" = "OPEN" ]; then
        log_to_file "✓ 数据库状态: $db_status"
    else
        log_to_file "✗ 数据库状态异常: $db_status"
        return 1
    fi
    
    # 检查归档模式
    local archive_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT LOG_MODE FROM V\$DATABASE;
EXIT;
EOF
)
    
    if [ "$archive_mode" = "ARCHIVELOG" ]; then
        log_to_file "✓ 归档模式: $archive_mode"
    else
        log_to_file "✗ 归档模式切换失败: $archive_mode"
        return 1
    fi
    
    # 检查归档目标（FRA模式）
    local archive_dest=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT DESTINATION FROM V\$ARCHIVE_DEST WHERE DEST_ID=1;
EXIT;
EOF
)

    if [[ "$archive_dest" == *"USE_DB_RECOVERY_FILE_DEST"* ]]; then
        log_to_file "✓ 归档目标: $archive_dest (FRA模式)"
    else
        log_to_file "✗ 归档目标配置异常: $archive_dest"
        return 1
    fi

    # 检查FRA使用情况
    local fra_info=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT ROUND((SPACE_USED/SPACE_LIMIT)*100, 2) FROM V\$RECOVERY_FILE_DEST;
EXIT;
EOF
)
    log_to_file "FRA使用率: ${fra_info}%"
    
    log_to_file "归档模式切换验证完成"
    return 0
}

# 主函数
main() {
    log_to_file "========================================="
    log_to_file "Oracle数据库归档模式切换开始"
    log_to_file "开始时间: $(date)"
    log_to_file "执行用户: $(whoami)"
    log_to_file "脚本路径: $0"
    log_to_file "日志文件: $LOGFILE"
    log_to_file "========================================="
    
    # 显示重要警告
    echo "========================================="
    echo "重要警告："
    echo "1. 此操作将重启数据库"
    echo "2. 请确保在维护窗口期间执行"
    echo "3. 请确保没有重要业务正在运行"
    echo "4. 建议先在测试环境验证"
    echo "========================================="
    echo "是否继续执行归档模式切换？(y/N)"
    
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        log_to_file "用户取消操作"
        echo "操作已取消"
        exit 0
    fi
    
    # 执行各个步骤
    if ! check_prerequisites; then
        log_to_file "前置条件检查失败，退出"
        exit 1
    fi
    
    backup_important_files
    
    if ! execute_archivelog_switch; then
        log_to_file "归档模式切换失败，退出"
        exit 1
    fi
    
    if ! verify_archivelog_mode; then
        log_to_file "归档模式验证失败，请检查日志"
        exit 1
    fi
    
    log_to_file "========================================="
    log_to_file "Oracle数据库归档模式切换成功完成（单FRA模式）"
    log_to_file "完成时间: $(date)"
    log_to_file "日志文件: $LOGFILE"
    log_to_file "FRA目录: $FRA_DIR"
    log_to_file "========================================="

    echo "========================================="
    echo "归档模式切换成功完成（单FRA模式）！"
    echo "详细日志请查看: $LOGFILE"
    echo "FRA目录: $FRA_DIR"
    echo "归档日志将自动管理，无需手动清理"
    echo "现在可以执行在线备份操作"
    echo "========================================="
}

# 执行主函数
main "$@"
