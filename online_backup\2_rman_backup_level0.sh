#!/bin/bash

# Oracle 11gR2 RMAN Level 0 全量在线备份脚本 - 企业级优化版
# 功能：执行完整的Level 0增量备份，包含数据库、控制文件、参数文件、归档日志
# 特点：在线备份，数据库保持运行状态，支持7x24业务连续性

# =============================================================================
# 脚本配置和环境加载
# =============================================================================

# 获取脚本目录并加载环境配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/0_oracle_env.sh"

# 备份配置
BACKUP_DATE=$(date +%Y%m%d)
BACKUP_TIME=$(date +%H%M%S)
BACKUP_TIMESTAMP="${BACKUP_DATE}_${BACKUP_TIME}"
LOCK_FILE="/tmp/rman_backup_level0.lock"
LOGFILE="$BACKUP_LOG_DIR/level0_full_backup_$BACKUP_TIMESTAMP.log"

# 备份标签和格式
BACKUP_TAG="LEVEL0_FULL_BACKUP_$BACKUP_DATE"
DB_FORMAT="$BACKUP_BASE_DIR/level0/level0_%d_%T_%U.bkp"
ARCH_FORMAT="$BACKUP_BASE_DIR/archivelogs/arch_%d_%T_%U.arc"
CTL_FORMAT="$BACKUP_BASE_DIR/controlfiles/ctlfile_%d_%T.ctl"
SPFILE_FORMAT="$BACKUP_BASE_DIR/config/spfile_%d_%T_%U.bkp"

# 创建备份目录结构
mkdir -p "$BACKUP_BASE_DIR/level0"
mkdir -p "$BACKUP_BASE_DIR/archivelogs"
mkdir -p "$BACKUP_BASE_DIR/controlfiles"
mkdir -p "$BACKUP_BASE_DIR/config"
mkdir -p "$BACKUP_LOG_DIR"

# =============================================================================
# 日志和工具函数
# =============================================================================

# 日志函数
log_backup() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOGFILE"
}

log_backup_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$LOGFILE" >&2
}

log_backup_success() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1" | tee -a "$LOGFILE"
}

# 清理函数
cleanup() {
    log_backup "执行清理操作..."
    rm -f "$LOCK_FILE"

    # 如果备份失败，记录失败信息
    if [ $? -ne 0 ]; then
        log_backup_error "备份过程中发生错误，请检查日志文件: $LOGFILE"

        # 发送告警通知（可根据需要实现）
        echo "Oracle Level 0 备份失败 - $(date)" >> "$BACKUP_LOG_DIR/backup_failures.log"
    fi
}

# 设置信号处理
trap cleanup EXIT INT TERM

# =============================================================================
# 前置检查函数
# =============================================================================

# 检查备份前置条件
check_backup_prerequisites() {
    log_backup "========================================="
    log_backup "检查Level 0备份前置条件"
    log_backup "========================================="

    # 检查锁文件
    if [ -f "$LOCK_FILE" ]; then
        local lock_pid=$(cat "$LOCK_FILE" 2>/dev/null)
        if [ -n "$lock_pid" ] && kill -0 "$lock_pid" 2>/dev/null; then
            log_backup_error "Level 0备份已在运行中 (PID: $lock_pid)"
            return 1
        else
            log_backup "清理过期的锁文件"
            rm -f "$LOCK_FILE"
        fi
    fi

    # 创建锁文件
    echo $$ > "$LOCK_FILE"
    log_backup "创建锁文件: $LOCK_FILE (PID: $$)"

    # 检查Oracle环境
    if ! check_oracle_environment; then
        log_backup_error "Oracle环境检查失败"
        return 1
    fi

    # 检查数据库状态
    local db_status=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT STATUS FROM V\$INSTANCE;
EXIT;
EOF
)

    if [ "$db_status" != "OPEN" ]; then
        log_backup_error "数据库状态异常: $db_status (需要OPEN状态)"
        return 1
    else
        log_backup_success "数据库状态: $db_status"
    fi

    # 检查归档模式
    local archive_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT LOG_MODE FROM V\$DATABASE;
EXIT;
EOF
)

    if [ "$archive_mode" != "ARCHIVELOG" ]; then
        log_backup_error "数据库未启用归档模式: $archive_mode"
        log_backup_error "在线备份需要数据库处于归档模式"
        return 1
    else
        log_backup_success "数据库归档模式: $archive_mode"
    fi

    # 检查磁盘空间
    if ! check_disk_space; then
        log_backup_error "磁盘空间检查失败"
        return 1
    fi

    # 检查RMAN连接
    if ! rman target / <<< "EXIT;" > /dev/null 2>&1; then
        log_backup_error "无法连接到RMAN"
        return 1
    else
        log_backup_success "RMAN连接正常"
    fi

    log_backup "前置条件检查完成"
    return 0
}

# 备份前数据库信息收集
collect_database_info() {
    log_backup "========================================="
    log_backup "收集数据库信息"
    log_backup "========================================="

    local info_file="$BACKUP_LOG_DIR/db_info_before_backup_$BACKUP_TIMESTAMP.txt"

    sqlplus -s / as sysdba <<EOF > "$info_file" 2>&1
SET PAGESIZE 1000 LINESIZE 200
SPOOL $info_file
SELECT 'BACKUP START TIME: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS INFO FROM DUAL;
SELECT 'DATABASE INFO:' AS SECTION FROM DUAL;
SELECT NAME, DBID, CREATED, LOG_MODE, OPEN_MODE FROM V\$DATABASE;
SELECT 'INSTANCE INFO:' AS SECTION FROM DUAL;
SELECT INSTANCE_NAME, STATUS, STARTUP_TIME FROM V\$INSTANCE;
SELECT 'DATAFILE INFO:' AS SECTION FROM DUAL;
SELECT FILE_ID, FILE_NAME, TABLESPACE_NAME, BYTES/1024/1024 AS SIZE_MB, STATUS FROM DBA_DATA_FILES ORDER BY FILE_ID;
SELECT 'TABLESPACE INFO:' AS SECTION FROM DUAL;
SELECT TABLESPACE_NAME, STATUS, CONTENTS, EXTENT_MANAGEMENT FROM DBA_TABLESPACES;
SELECT 'LOG FILE INFO:' AS SECTION FROM DUAL;
SELECT GROUP#, THREAD#, SEQUENCE#, BYTES/1024/1024 AS SIZE_MB, STATUS, ARCHIVED FROM V\$LOG ORDER BY GROUP#;
SPOOL OFF
EXIT;
EOF

    log_backup "数据库信息已保存到: $info_file"
}

# =============================================================================
# 核心备份函数
# =============================================================================

# 执行Level 0备份
execute_level0_backup() {
    log_backup "========================================="
    log_backup "开始执行RMAN Level 0全量备份"
    log_backup "========================================="

    log_backup "备份标签: $BACKUP_TAG"
    log_backup "数据库备份格式: $DB_FORMAT"
    log_backup "归档日志备份格式: $ARCH_FORMAT"
    log_backup "控制文件备份格式: $CTL_FORMAT"
    log_backup "参数文件备份格式: $SPFILE_FORMAT"

    # 执行RMAN备份
    rman target / log="$LOGFILE" <<EOF
# 配置RMAN参数（适用于Standard Edition）
CONFIGURE RETENTION POLICY TO RECOVERY WINDOW OF 30 DAYS;
CONFIGURE BACKUP OPTIMIZATION ON;
CONFIGURE DEFAULT DEVICE TYPE TO DISK;
CONFIGURE CONTROLFILE AUTOBACKUP ON;
CONFIGURE CONTROLFILE AUTOBACKUP FORMAT FOR DEVICE TYPE DISK TO '$CTL_FORMAT';
CONFIGURE ARCHIVELOG DELETION POLICY TO BACKED UP 1 TIMES TO DISK;

# 交叉检查现有备份
CROSSCHECK BACKUP;
CROSSCHECK ARCHIVELOG ALL;

# 删除过期备份
DELETE NOPROMPT EXPIRED BACKUP;
DELETE NOPROMPT EXPIRED ARCHIVELOG ALL;

# 执行Level 0增量备份（全量备份）- Standard Edition兼容版本
RUN {
  # 分配单个通道（Standard Edition限制）
  ALLOCATE CHANNEL c1 DEVICE TYPE DISK MAXPIECESIZE 2G;

  # 执行Level 0增量备份（全量备份）
  BACKUP INCREMENTAL LEVEL 0 AS COMPRESSED BACKUPSET DATABASE
    FORMAT '$DB_FORMAT'
    TAG='$BACKUP_TAG'
    PLUS ARCHIVELOG
    FORMAT '$ARCH_FORMAT'
    TAG='${BACKUP_TAG}_ARCH'
    DELETE INPUT;

  # 备份当前控制文件
  BACKUP CURRENT CONTROLFILE
    FORMAT '$CTL_FORMAT'
    TAG='${BACKUP_TAG}_CTL';

  # 备份SPFILE
  BACKUP SPFILE
    FORMAT '$SPFILE_FORMAT'
    TAG='${BACKUP_TAG}_SPFILE';

  # 释放通道
  RELEASE CHANNEL c1;
}

# 创建控制文件的文本备份
SQL "ALTER DATABASE BACKUP CONTROLFILE TO TRACE AS ''$BACKUP_BASE_DIR/config/create_controlfile_$BACKUP_TIMESTAMP.sql''";

# 验证备份
RESTORE DATABASE VALIDATE;

# 列出备份信息
LIST BACKUP SUMMARY;

# 报告备份统计信息
REPORT SCHEMA;

# 清理过期备份
DELETE NOPROMPT OBSOLETE;

EXIT;
EOF

    local rman_exit_code=$?

    if [ $rman_exit_code -eq 0 ]; then
        log_backup_success "RMAN Level 0备份执行完成"
        return 0
    else
        log_backup_error "RMAN Level 0备份执行失败，退出代码: $rman_exit_code"
        return 1
    fi
}

# 备份后验证
verify_backup() {
    log_backup "========================================="
    log_backup "验证备份完整性"
    log_backup "========================================="

    # 验证备份文件
    local backup_files_count=$(find "$BACKUP_BASE_DIR" -name "*$BACKUP_DATE*" -type f | wc -l)
    log_backup "备份文件数量: $backup_files_count"

    if [ "$backup_files_count" -eq 0 ]; then
        log_backup_error "未找到备份文件"
        return 1
    fi

    # 使用RMAN验证备份
    log_backup "使用RMAN验证备份完整性..."
    rman target / <<EOF >> "$LOGFILE" 2>&1
CROSSCHECK BACKUP;
VALIDATE BACKUPSET TAG='$BACKUP_TAG';
LIST BACKUP TAG='$BACKUP_TAG';
EXIT;
EOF

    if [ $? -eq 0 ]; then
        log_backup_success "备份验证通过"
    else
        log_backup_error "备份验证失败"
        return 1
    fi

    return 0
}

# 生成备份报告
generate_backup_report() {
    log_backup "========================================="
    log_backup "生成备份报告"
    log_backup "========================================="

    local report_file="$BACKUP_LOG_DIR/level0_backup_report_$BACKUP_TIMESTAMP.txt"

    cat > "$report_file" <<EOF
Oracle Database Level 0 Backup Report
=====================================
Backup Date: $BACKUP_DATE
Backup Time: $BACKUP_TIME
Oracle SID: $ORACLE_SID
Oracle Home: $ORACLE_HOME
Backup Tag: $BACKUP_TAG

Backup Directories:
- Base Directory: $BACKUP_BASE_DIR
- Database Backups: $BACKUP_BASE_DIR/level0/
- Archive Logs: $BACKUP_BASE_DIR/archivelogs/
- Control Files: $BACKUP_BASE_DIR/controlfiles/
- Config Files: $BACKUP_BASE_DIR/config/

Backup Files Summary:
EOF

    # 添加文件列表
    echo "Database Backup Files:" >> "$report_file"
    find "$BACKUP_BASE_DIR/level0" -name "*$BACKUP_DATE*" -type f -exec ls -lh {} \; >> "$report_file" 2>/dev/null

    echo -e "\nArchive Log Files:" >> "$report_file"
    find "$BACKUP_BASE_DIR/archivelogs" -name "*$BACKUP_DATE*" -type f -exec ls -lh {} \; >> "$report_file" 2>/dev/null

    echo -e "\nControl Files:" >> "$report_file"
    find "$BACKUP_BASE_DIR/controlfiles" -name "*$BACKUP_DATE*" -type f -exec ls -lh {} \; >> "$report_file" 2>/dev/null

    echo -e "\nConfiguration Files:" >> "$report_file"
    find "$BACKUP_BASE_DIR/config" -name "*$BACKUP_DATE*" -type f -exec ls -lh {} \; >> "$report_file" 2>/dev/null

    # 计算总备份大小
    local total_size=$(du -sh "$BACKUP_BASE_DIR" 2>/dev/null | cut -f1)
    echo -e "\nTotal Backup Size: $total_size" >> "$report_file"

    # 添加数据库信息
    echo -e "\nDatabase Information:" >> "$report_file"
    sqlplus -s / as sysdba <<EOF >> "$report_file" 2>&1
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT 'Database Name: ' || NAME FROM V\$DATABASE;
SELECT 'Database ID: ' || DBID FROM V\$DATABASE;
SELECT 'Archive Mode: ' || LOG_MODE FROM V\$DATABASE;
SELECT 'Database Size: ' || ROUND(SUM(BYTES)/1024/1024/1024,2) || ' GB' FROM DBA_DATA_FILES;
EXIT;
EOF

    echo -e "\nBackup Completion Time: $(date)" >> "$report_file"
    echo -e "Log File: $LOGFILE" >> "$report_file"

    log_backup "备份报告已生成: $report_file"
}

# 清理旧备份文件
cleanup_old_backups() {
    log_backup "========================================="
    log_backup "清理旧备份文件"
    log_backup "========================================="

    # 清理30天前的备份文件（保留策略）
    local cleanup_date=$(date -d '30 days ago' +%Y%m%d 2>/dev/null || date -v-30d +%Y%m%d 2>/dev/null)

    if [ -n "$cleanup_date" ]; then
        log_backup "清理 $cleanup_date 之前的备份文件..."

        # 清理各个目录中的旧文件
        find "$BACKUP_BASE_DIR/level0" -name "*" -type f -mtime +30 -delete 2>/dev/null
        find "$BACKUP_BASE_DIR/archivelogs" -name "*" -type f -mtime +30 -delete 2>/dev/null
        find "$BACKUP_BASE_DIR/controlfiles" -name "*" -type f -mtime +30 -delete 2>/dev/null
        find "$BACKUP_LOG_DIR" -name "*.log" -type f -mtime +30 -delete 2>/dev/null

        log_backup "旧备份文件清理完成"
    else
        log_backup "无法确定清理日期，跳过旧文件清理"
    fi
}

# 发送备份通知（可扩展）
send_backup_notification() {
    local status=$1
    local message=$2

    # 这里可以实现邮件通知、短信通知等
    # 目前只记录到日志
    log_backup "备份通知: $status - $message"

    # 示例：写入通知日志
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Level 0 Backup $status: $message" >> "$BACKUP_LOG_DIR/backup_notifications.log"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_backup "========================================="
    log_backup "Oracle Database Level 0 在线备份开始"
    log_backup "开始时间: $(date)"
    log_backup "执行用户: $(whoami)"
    log_backup "脚本路径: $0"
    log_backup "进程ID: $$"
    log_backup "日志文件: $LOGFILE"
    log_backup "========================================="

    local backup_start_time=$(date +%s)
    local backup_failed=0

    # 执行备份前检查
    if ! check_backup_prerequisites; then
        log_backup_error "备份前置条件检查失败"
        send_backup_notification "FAILED" "前置条件检查失败"
        exit 1
    fi

    # 收集数据库信息
    collect_database_info

    # 执行Level 0备份
    if ! execute_level0_backup; then
        log_backup_error "Level 0备份执行失败"
        backup_failed=1
    fi

    # 验证备份
    if [ $backup_failed -eq 0 ]; then
        if ! verify_backup; then
            log_backup_error "备份验证失败"
            backup_failed=1
        fi
    fi

    # 生成备份报告
    generate_backup_report

    # 清理旧备份
    cleanup_old_backups

    # 计算备份耗时
    local backup_end_time=$(date +%s)
    local backup_duration=$((backup_end_time - backup_start_time))
    local backup_duration_formatted=$(printf "%02d:%02d:%02d" $((backup_duration/3600)) $((backup_duration%3600/60)) $((backup_duration%60)))

    # 最终状态报告
    log_backup "========================================="
    if [ $backup_failed -eq 0 ]; then
        log_backup_success "Oracle Database Level 0 在线备份成功完成"
        log_backup "完成时间: $(date)"
        log_backup "备份耗时: $backup_duration_formatted"
        log_backup "备份位置: $BACKUP_BASE_DIR"
        log_backup "日志文件: $LOGFILE"

        send_backup_notification "SUCCESS" "Level 0备份成功完成，耗时: $backup_duration_formatted"

        # 显示备份统计
        local total_size=$(du -sh "$BACKUP_BASE_DIR" 2>/dev/null | cut -f1)
        log_backup "总备份大小: $total_size"

        # 显示下次备份建议
        local next_level1_date=$(date -d '+1 day' +%Y-%m-%d 2>/dev/null || date -v+1d +%Y-%m-%d 2>/dev/null)
        log_backup "建议下次Level 1增量备份时间: $next_level1_date"

    else
        log_backup_error "Oracle Database Level 0 在线备份失败"
        log_backup "失败时间: $(date)"
        log_backup "备份耗时: $backup_duration_formatted"
        log_backup "错误日志: $LOGFILE"

        send_backup_notification "FAILED" "Level 0备份失败，请检查日志: $LOGFILE"

        exit 1
    fi

    log_backup "========================================="

    # 显示成功消息到控制台
    echo "========================================="
    echo "Oracle Level 0 在线备份成功完成！"
    echo "备份时间: $backup_duration_formatted"
    echo "备份大小: $(du -sh "$BACKUP_BASE_DIR" 2>/dev/null | cut -f1)"
    echo "详细日志: $LOGFILE"
    echo "备份报告: $BACKUP_LOG_DIR/level0_backup_report_$BACKUP_TIMESTAMP.txt"
    echo "========================================="
}

# 执行主函数
main "$@"
