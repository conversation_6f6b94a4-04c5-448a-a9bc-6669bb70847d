#!/bin/bash

# Oracle 11gR2 RMAN Level 1 增量在线备份脚本 - 企业级优化版
# 功能：执行Level 1差异增量备份，只备份自上次Level 0或Level 1备份以来的变化数据
# 特点：快速备份，节省存储空间，支持快速恢复

# =============================================================================
# 脚本配置和环境加载
# =============================================================================

# 获取脚本目录并加载环境配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/0_oracle_env.sh"

# 备份配置
BACKUP_DATE=$(date +%Y%m%d)
BACKUP_TIME=$(date +%H%M%S)
BACKUP_TIMESTAMP="${BACKUP_DATE}_${BACKUP_TIME}"
LOCK_FILE="/tmp/rman_backup_level1.lock"
LOGFILE="$BACKUP_LOG_DIR/level1_incremental_backup_$BACKUP_TIMESTAMP.log"

# 备份标签和格式（标签长度限制在31字符以内）
BACKUP_TAG="L1_$BACKUP_DATE"
DB_FORMAT="$BACKUP_BASE_DIR/level1/level1_%d_%T_%U.bkp"
ARCH_FORMAT="$BACKUP_BASE_DIR/archivelogs/arch_l1_%d_%T_%U.arc"
CTL_FORMAT="$BACKUP_BASE_DIR/controlfiles/ctlfile_l1_%d_%T.ctl"

# 创建备份目录结构
mkdir -p "$BACKUP_BASE_DIR/level1"
mkdir -p "$BACKUP_BASE_DIR/archivelogs"
mkdir -p "$BACKUP_BASE_DIR/controlfiles"
mkdir -p "$BACKUP_LOG_DIR"

# =============================================================================
# 日志和工具函数
# =============================================================================

# 日志函数
log_backup() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOGFILE"
}

log_backup_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$LOGFILE" >&2
}

log_backup_success() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1" | tee -a "$LOGFILE"
}

log_backup_warning() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] WARNING: $1" | tee -a "$LOGFILE"
}

# 清理函数
cleanup() {
    log_backup "执行清理操作..."
    rm -f "$LOCK_FILE"

    if [ $? -ne 0 ]; then
        log_backup_error "Level 1备份过程中发生错误，请检查日志文件: $LOGFILE"
        echo "Oracle Level 1 增量备份失败 - $(date)" >> "$BACKUP_LOG_DIR/backup_failures.log"
    fi
}

# 设置信号处理
trap cleanup EXIT INT TERM

# =============================================================================
# 前置检查函数
# =============================================================================

# 检查Level 1备份前置条件
check_level1_prerequisites() {
    log_backup "========================================="
    log_backup "检查Level 1增量备份前置条件"
    log_backup "========================================="

    # 检查锁文件
    if [ -f "$LOCK_FILE" ]; then
        local lock_pid=$(cat "$LOCK_FILE" 2>/dev/null)
        if [ -n "$lock_pid" ] && kill -0 "$lock_pid" 2>/dev/null; then
            log_backup_error "Level 1增量备份已在运行中 (PID: $lock_pid)"
            return 1
        else
            log_backup "清理过期的锁文件"
            rm -f "$LOCK_FILE"
        fi
    fi

    # 创建锁文件
    echo $$ > "$LOCK_FILE"
    log_backup "创建锁文件: $LOCK_FILE (PID: $$)"

    # 检查Oracle环境
    if ! check_oracle_environment; then
        log_backup_error "Oracle环境检查失败"
        return 1
    fi

    # 检查数据库状态
    local db_status=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT STATUS FROM V\$INSTANCE;
EXIT;
EOF
)

    if [ "$db_status" != "OPEN" ]; then
        log_backup_error "数据库状态异常: $db_status (需要OPEN状态)"
        return 1
    else
        log_backup_success "数据库状态: $db_status"
    fi

    # 检查归档模式
    local archive_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT LOG_MODE FROM V\$DATABASE;
EXIT;
EOF
)

    if [ "$archive_mode" != "ARCHIVELOG" ]; then
        log_backup_error "数据库未启用归档模式: $archive_mode"
        return 1
    else
        log_backup_success "数据库归档模式: $archive_mode"
    fi

    # 检查是否存在Level 0备份
    log_backup "检查Level 0备份基础..."
    rman target / <<EOF > /tmp/check_level0.log 2>&1
LIST BACKUP SUMMARY;
EXIT;
EOF

    if grep -q "LEVEL 0" /tmp/check_level0.log; then
        log_backup_success "找到Level 0备份基础"
    else
        log_backup_warning "未找到Level 0备份，Level 1备份可能无效"
        log_backup_warning "建议先执行Level 0全量备份"

        echo "警告：未找到Level 0备份基础"
        echo "Level 1增量备份需要基于Level 0备份"
        echo "是否继续执行？(y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log_backup "用户选择退出"
            return 1
        fi
    fi

    # 检查磁盘空间（增量备份需要的空间较少）
    local backup_space=$(df -BG "$BACKUP_BASE_DIR" 2>/dev/null | awk 'NR==2 {print $4}' | tr -d 'G')
    if [ -n "$backup_space" ] && [ "$backup_space" -gt 0 ]; then
        if [ "$backup_space" -lt 5 ]; then
            log_backup_error "备份目录可用空间不足: ${backup_space}GB (增量备份至少需要5GB)"
            return 1
        else
            log_backup_success "备份目录可用空间: ${backup_space}GB"
        fi
    fi

    # 检查RMAN连接
    if ! rman target / <<< "EXIT;" > /dev/null 2>&1; then
        log_backup_error "无法连接到RMAN"
        return 1
    else
        log_backup_success "RMAN连接正常"
    fi

    log_backup "Level 1备份前置条件检查完成"
    return 0
}

# 检查上次备份信息
check_last_backup_info() {
    log_backup "========================================="
    log_backup "检查上次备份信息"
    log_backup "========================================="

    local backup_info_file="$BACKUP_LOG_DIR/last_backup_info_$BACKUP_TIMESTAMP.txt"

    rman target / <<EOF > "$backup_info_file" 2>&1
LIST BACKUP SUMMARY;
LIST BACKUP BY FILE;
REPORT SCHEMA;
EXIT;
EOF

    # 分析最近的备份
    if grep -q "LEVEL 0" "$backup_info_file"; then
        local last_level0=$(grep "LEVEL 0" "$backup_info_file" | tail -1)
        log_backup "最近的Level 0备份: $last_level0"
    fi

    if grep -q "LEVEL 1" "$backup_info_file"; then
        local last_level1=$(grep "LEVEL 1" "$backup_info_file" | tail -1)
        log_backup "最近的Level 1备份: $last_level1"
    fi

    log_backup "备份信息已保存到: $backup_info_file"
}

# =============================================================================
# 核心备份函数
# =============================================================================

# 执行Level 1增量备份
execute_level1_backup() {
    log_backup "========================================="
    log_backup "开始执行RMAN Level 1增量备份"
    log_backup "========================================="

    log_backup "备份标签: $BACKUP_TAG"
    log_backup "数据库备份格式: $DB_FORMAT"
    log_backup "归档日志备份格式: $ARCH_FORMAT"
    log_backup "控制文件备份格式: $CTL_FORMAT"

    # 执行RMAN增量备份（Oracle 11g Standard Edition兼容版本）
    rman target / log="$LOGFILE" <<EOF
# 配置RMAN参数
CONFIGURE RETENTION POLICY TO RECOVERY WINDOW OF 30 DAYS;
CONFIGURE BACKUP OPTIMIZATION ON;
CONFIGURE DEFAULT DEVICE TYPE TO DISK;
CONFIGURE CONTROLFILE AUTOBACKUP ON;
CONFIGURE CONTROLFILE AUTOBACKUP FORMAT FOR DEVICE TYPE DISK TO '$CTL_FORMAT';

# 交叉检查现有备份
CROSSCHECK BACKUP;
CROSSCHECK ARCHIVELOG ALL;

# 删除过期备份
DELETE NOPROMPT EXPIRED BACKUP;
DELETE NOPROMPT EXPIRED ARCHIVELOG ALL;

# 执行Level 1差异增量备份（使用默认通道）
BACKUP INCREMENTAL LEVEL 1 DATABASE
  FORMAT '$DB_FORMAT'
  TAG '$BACKUP_TAG'
  PLUS ARCHIVELOG
  FORMAT '$ARCH_FORMAT'
  TAG '${BACKUP_TAG}_ARC'
  DELETE INPUT;

# 备份当前控制文件
BACKUP CURRENT CONTROLFILE
  FORMAT '$CTL_FORMAT'
  TAG '${BACKUP_TAG}_CTL';

# 列出备份信息
LIST BACKUP SUMMARY;

# 显示增量备份统计
REPORT SCHEMA;

# 清理过期备份
DELETE NOPROMPT OBSOLETE;

EXIT;
EOF

    local rman_exit_code=$?

    if [ $rman_exit_code -eq 0 ]; then
        log_backup_success "RMAN Level 1增量备份执行完成"
        return 0
    else
        log_backup_error "RMAN Level 1增量备份执行失败，退出代码: $rman_exit_code"
        return 1
    fi
}

# 验证增量备份
verify_incremental_backup() {
    log_backup "========================================="
    log_backup "验证Level 1增量备份"
    log_backup "========================================="

    # 检查备份文件
    local backup_files_count=$(find "$BACKUP_BASE_DIR/level1" -name "*$BACKUP_DATE*" -type f | wc -l)
    log_backup "Level 1备份文件数量: $backup_files_count"

    if [ "$backup_files_count" -eq 0 ]; then
        log_backup_error "未找到Level 1备份文件"
        return 1
    fi

    # 使用RMAN验证备份
    log_backup "使用RMAN验证增量备份完整性..."
    rman target / <<EOF >> "$LOGFILE" 2>&1
CROSSCHECK BACKUP;
VALIDATE BACKUPSET TAG='$BACKUP_TAG';
LIST BACKUP TAG='$BACKUP_TAG';
EXIT;
EOF

    if [ $? -eq 0 ]; then
        log_backup_success "增量备份验证通过"
    else
        log_backup_error "增量备份验证失败"
        return 1
    fi

    # 检查备份大小（增量备份应该比全量备份小）
    local level1_size=$(du -sh "$BACKUP_BASE_DIR/level1" 2>/dev/null | cut -f1)
    local level0_size=$(du -sh "$BACKUP_BASE_DIR/level0" 2>/dev/null | cut -f1)

    log_backup "Level 1备份大小: $level1_size"
    if [ -n "$level0_size" ]; then
        log_backup "Level 0备份大小: $level0_size (参考)"
    fi

    return 0
}

# 生成增量备份报告
generate_incremental_report() {
    log_backup "========================================="
    log_backup "生成Level 1增量备份报告"
    log_backup "========================================="

    local report_file="$BACKUP_LOG_DIR/level1_backup_report_$BACKUP_TIMESTAMP.txt"

    cat > "$report_file" <<EOF
Oracle Database Level 1 Incremental Backup Report
=================================================
Backup Date: $BACKUP_DATE
Backup Time: $BACKUP_TIME
Oracle SID: $ORACLE_SID
Oracle Home: $ORACLE_HOME
Backup Tag: $BACKUP_TAG
Backup Type: Level 1 Differential Incremental

Backup Directories:
- Base Directory: $BACKUP_BASE_DIR
- Level 1 Backups: $BACKUP_BASE_DIR/level1/
- Archive Logs: $BACKUP_BASE_DIR/archivelogs/
- Control Files: $BACKUP_BASE_DIR/controlfiles/

Level 1 Backup Files:
EOF

    # 添加Level 1备份文件列表
    find "$BACKUP_BASE_DIR/level1" -name "*$BACKUP_DATE*" -type f -exec ls -lh {} \; >> "$report_file" 2>/dev/null

    echo -e "\nArchive Log Files (Level 1):" >> "$report_file"
    find "$BACKUP_BASE_DIR/archivelogs" -name "*l1*$BACKUP_DATE*" -type f -exec ls -lh {} \; >> "$report_file" 2>/dev/null

    echo -e "\nControl Files (Level 1):" >> "$report_file"
    find "$BACKUP_BASE_DIR/controlfiles" -name "*l1*$BACKUP_DATE*" -type f -exec ls -lh {} \; >> "$report_file" 2>/dev/null

    # 计算增量备份大小
    local level1_size=$(du -sh "$BACKUP_BASE_DIR/level1" 2>/dev/null | cut -f1)
    echo -e "\nLevel 1 Backup Size: $level1_size" >> "$report_file"

    # 添加备份效率信息
    echo -e "\nBackup Efficiency:" >> "$report_file"
    local level0_size=$(du -sh "$BACKUP_BASE_DIR/level0" 2>/dev/null | cut -f1)
    if [ -n "$level0_size" ]; then
        echo "Level 0 Size: $level0_size" >> "$report_file"
        echo "Level 1 Size: $level1_size" >> "$report_file"
        echo "Space Savings: Significant (incremental backup only contains changed data)" >> "$report_file"
    fi

    # 添加数据库变化信息
    echo -e "\nDatabase Change Information:" >> "$report_file"
    sqlplus -s / as sysdba <<EOF >> "$report_file" 2>&1
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT 'Database Name: ' || NAME FROM V\$DATABASE;
SELECT 'Current SCN: ' || CURRENT_SCN FROM V\$DATABASE;
SELECT 'Archive Log Sequence: ' || MAX(SEQUENCE#) FROM V\$ARCHIVED_LOG WHERE ARCHIVED='YES';
EXIT;
EOF

    echo -e "\nBackup Completion Time: $(date)" >> "$report_file"
    echo -e "Log File: $LOGFILE" >> "$report_file"

    # 添加恢复建议
    echo -e "\nRecovery Notes:" >> "$report_file"
    echo "- This Level 1 backup can be used with the base Level 0 backup for point-in-time recovery" >> "$report_file"
    echo "- Archive logs are included for complete recovery capability" >> "$report_file"
    echo "- Next recommended backup: Level 1 incremental (daily) or Level 0 (weekly)" >> "$report_file"

    log_backup "Level 1备份报告已生成: $report_file"
}

# 更新备份策略建议
update_backup_strategy() {
    log_backup "========================================="
    log_backup "更新备份策略建议"
    log_backup "========================================="

    local strategy_file="$BACKUP_LOG_DIR/backup_strategy_recommendations.txt"

    # 分析备份历史
    local level0_count=$(find "$BACKUP_BASE_DIR/level0" -name "*.bkp" -type f | wc -l)
    local level1_count=$(find "$BACKUP_BASE_DIR/level1" -name "*.bkp" -type f | wc -l)

    cat >> "$strategy_file" <<EOF

Backup Strategy Update - $(date)
================================
Current Backup Status:
- Level 0 backups: $level0_count
- Level 1 backups: $level1_count
- Last Level 1 backup: $BACKUP_TIMESTAMP

Recommendations:
1. Continue daily Level 1 incremental backups
2. Perform weekly Level 0 full backups
3. Monitor backup sizes and adjust retention policy as needed
4. Test restore procedures regularly

Next Actions:
- Next Level 1 backup: $(date -d '+1 day' +%Y-%m-%d 2>/dev/null || date -v+1d +%Y-%m-%d 2>/dev/null)
- Next Level 0 backup: $(date -d '+7 days' +%Y-%m-%d 2>/dev/null || date -v+7d +%Y-%m-%d 2>/dev/null)

EOF

    log_backup "备份策略建议已更新: $strategy_file"
}

# 发送备份通知
send_incremental_notification() {
    local status=$1
    local message=$2

    log_backup "增量备份通知: $status - $message"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Level 1 Incremental Backup $status: $message" >> "$BACKUP_LOG_DIR/backup_notifications.log"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log_backup "========================================="
    log_backup "Oracle Database Level 1 增量在线备份开始"
    log_backup "开始时间: $(date)"
    log_backup "执行用户: $(whoami)"
    log_backup "脚本路径: $0"
    log_backup "进程ID: $$"
    log_backup "日志文件: $LOGFILE"
    log_backup "========================================="

    local backup_start_time=$(date +%s)
    local backup_failed=0

    # 执行备份前检查
    if ! check_level1_prerequisites; then
        log_backup_error "Level 1备份前置条件检查失败"
        send_incremental_notification "FAILED" "前置条件检查失败"
        exit 1
    fi

    # 检查上次备份信息
    check_last_backup_info

    # 执行Level 1增量备份
    if ! execute_level1_backup; then
        log_backup_error "Level 1增量备份执行失败"
        backup_failed=1
    fi

    # 验证备份
    if [ $backup_failed -eq 0 ]; then
        if ! verify_incremental_backup; then
            log_backup_error "增量备份验证失败"
            backup_failed=1
        fi
    fi

    # 生成备份报告
    generate_incremental_report

    # 更新备份策略
    update_backup_strategy

    # 计算备份耗时
    local backup_end_time=$(date +%s)
    local backup_duration=$((backup_end_time - backup_start_time))
    local backup_duration_formatted=$(printf "%02d:%02d:%02d" $((backup_duration/3600)) $((backup_duration%3600/60)) $((backup_duration%60)))

    # 最终状态报告
    log_backup "========================================="
    if [ $backup_failed -eq 0 ]; then
        log_backup_success "Oracle Database Level 1 增量在线备份成功完成"
        log_backup "完成时间: $(date)"
        log_backup "备份耗时: $backup_duration_formatted"
        log_backup "备份位置: $BACKUP_BASE_DIR/level1/"
        log_backup "日志文件: $LOGFILE"

        send_incremental_notification "SUCCESS" "Level 1增量备份成功完成，耗时: $backup_duration_formatted"

        # 显示备份统计
        local level1_size=$(du -sh "$BACKUP_BASE_DIR/level1" 2>/dev/null | cut -f1)
        log_backup "Level 1备份大小: $level1_size"

        # 显示下次备份建议
        local next_backup_date=$(date -d '+1 day' +%Y-%m-%d 2>/dev/null || date -v+1d +%Y-%m-%d 2>/dev/null)
        log_backup "建议下次增量备份时间: $next_backup_date"

        # 检查是否需要Level 0备份
        local days_since_level0=$(find "$BACKUP_BASE_DIR/level0" -name "*.bkp" -type f -mtime -7 | wc -l)
        if [ "$days_since_level0" -eq 0 ]; then
            log_backup_warning "建议执行Level 0全量备份（超过7天未执行）"
        fi

    else
        log_backup_error "Oracle Database Level 1 增量在线备份失败"
        log_backup "失败时间: $(date)"
        log_backup "备份耗时: $backup_duration_formatted"
        log_backup "错误日志: $LOGFILE"

        send_incremental_notification "FAILED" "Level 1增量备份失败，请检查日志: $LOGFILE"

        exit 1
    fi

    log_backup "========================================="

    # 显示成功消息到控制台
    echo "========================================="
    echo "Oracle Level 1 增量在线备份成功完成！"
    echo "备份时间: $backup_duration_formatted"
    echo "备份大小: $(du -sh "$BACKUP_BASE_DIR/level1" 2>/dev/null | cut -f1)"
    echo "详细日志: $LOGFILE"
    echo "备份报告: $BACKUP_LOG_DIR/level1_backup_report_$BACKUP_TIMESTAMP.txt"
    echo "========================================="

    # 显示备份效率信息
    local level0_size=$(du -sh "$BACKUP_BASE_DIR/level0" 2>/dev/null | cut -f1)
    if [ -n "$level0_size" ]; then
        echo "备份效率对比："
        echo "- Level 0 (全量): $level0_size"
        echo "- Level 1 (增量): $(du -sh "$BACKUP_BASE_DIR/level1" 2>/dev/null | cut -f1)"
        echo "- 存储节省: 显著（仅备份变化数据）"
        echo "========================================="
    fi
}

# 执行主函数
main "$@"
