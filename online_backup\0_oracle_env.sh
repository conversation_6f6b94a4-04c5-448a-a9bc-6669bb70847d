#!/bin/bash

# Oracle 在线备份环境配置和检查脚本 - 单FRA模式
# 功能：专门针对Flash Recovery Area的环境配置，实现单FRA模式归档
# 版本：Oracle Standard Edition兼容版本（支持单通道备份）

# =============================================================================
# 环境变量配置
# =============================================================================

# Oracle基础环境变量
export ORACLE_SID=PDBQZ
# export ORACLE_HOME=/opt/oracle/product/11gR2/db
# export PATH=$ORACLE_HOME/bin:$PATH
# export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH

# 备份相关环境变量
export BACKUP_BASE_DIR="/opt/oracle/rman_backup"
export FRA_DIR="/opt/oracle/flash_recovery_area"
export BACKUP_LOG_DIR="$BACKUP_BASE_DIR/logs"

# FRA配置参数
export FRA_SIZE="40G"
export FRA_USAGE_THRESHOLD=80

# 归档模式配置 - 单FRA模式
export ARCHIVE_MODE="FRA_ONLY"
export ARCHIVE_LOG_DIR="$FRA_DIR"  # 指向FRA目录

# 创建必要的目录
mkdir -p "$BACKUP_BASE_DIR"
mkdir -p "$FRA_DIR"
mkdir -p "$BACKUP_LOG_DIR"

# 设置FRA目录权限
chown oracle:oinstall "$FRA_DIR" 2>/dev/null || true
chmod 755 "$FRA_DIR" 2>/dev/null || true

# =============================================================================
# 环境检查函数
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查Oracle环境
check_oracle_environment() {
    log_info "开始Oracle环境检查..."
    
    # 检查当前用户
    local current_user=$(whoami)
    log_info "当前用户: $current_user"
    
    if [ "$current_user" != "oracle" ]; then
        log_warning "建议使用oracle用户运行备份脚本"
    fi
    
    # 检查环境变量
    log_info "检查环境变量..."
    if [ -z "$ORACLE_HOME" ]; then
        log_error "ORACLE_HOME未设置"
        return 1
    else
        log_success "ORACLE_HOME: $ORACLE_HOME"
    fi
    
    if [ -z "$ORACLE_SID" ]; then
        log_error "ORACLE_SID未设置"
        return 1
    else
        log_success "ORACLE_SID: $ORACLE_SID"
    fi
    
    # 检查Oracle主目录
    if [ ! -d "$ORACLE_HOME" ]; then
        log_error "Oracle主目录不存在: $ORACLE_HOME"
        return 1
    else
        log_success "Oracle主目录存在: $ORACLE_HOME"
    fi
    
    # 检查Oracle二进制文件
    if [ ! -f "$ORACLE_HOME/bin/sqlplus" ]; then
        log_error "sqlplus不存在: $ORACLE_HOME/bin/sqlplus"
        return 1
    else
        log_success "sqlplus可用"
    fi
    
    if [ ! -f "$ORACLE_HOME/bin/rman" ]; then
        log_error "rman不存在: $ORACLE_HOME/bin/rman"
        return 1
    else
        log_success "rman可用"
    fi
    
    return 0
}

# 检查数据库状态
check_database_status() {
    log_info "检查数据库状态..."

    local db_status=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT STATUS FROM V\$INSTANCE;
EXIT;
EOF
)

    if [ "$db_status" = "OPEN" ]; then
        log_success "数据库状态: $db_status"
    else
        log_warning "数据库状态: $db_status (非OPEN状态)"
    fi

    # 检查归档模式
    local archive_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT LOG_MODE FROM V\$DATABASE;
EXIT;
EOF
)

    if [ "$archive_mode" = "ARCHIVELOG" ]; then
        log_success "归档模式: $archive_mode"
    else
        log_warning "归档模式: $archive_mode (建议启用归档模式)"
    fi

    return 0
}

# 检查FRA配置
check_fra_configuration() {
    log_info "检查FRA配置..."

    # 检查FRA目录
    if [ ! -d "$FRA_DIR" ]; then
        log_warning "FRA目录不存在，正在创建: $FRA_DIR"
        mkdir -p "$FRA_DIR"
        chown oracle:oinstall "$FRA_DIR" 2>/dev/null || true
        chmod 755 "$FRA_DIR" 2>/dev/null || true
    else
        log_success "FRA目录存在: $FRA_DIR"
    fi

    # 检查FRA目录权限
    if [ ! -w "$FRA_DIR" ]; then
        log_error "FRA目录不可写: $FRA_DIR"
        return 1
    else
        log_success "FRA目录权限正常"
    fi

    # 检查FRA空间
    local fra_usage=$(df -h "$FRA_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$fra_usage" -gt "$FRA_USAGE_THRESHOLD" ]; then
        log_warning "FRA空间使用率过高: ${fra_usage}% (阈值: ${FRA_USAGE_THRESHOLD}%)"
    else
        log_success "FRA空间使用率正常: ${fra_usage}%"
    fi

    return 0
}

# 检查备份目录
check_backup_directories() {
    log_info "检查备份目录..."

    local dirs=("$BACKUP_BASE_DIR" "$BACKUP_LOG_DIR")

    # 根据归档模式添加相应目录
    case "$ARCHIVE_MODE" in
        "FRA_ONLY")
            dirs+=("$FRA_DIR")
            ;;
        "CUSTOM_ONLY")
            dirs+=("/opt/oracle/archivelog")
            ;;
        "HYBRID")
            dirs+=("$FRA_DIR" "/opt/oracle/archivelog")
            ;;
    esac

    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_warning "目录不存在，正在创建: $dir"
            mkdir -p "$dir"
            chown oracle:oinstall "$dir" 2>/dev/null || true
        else
            log_success "目录存在: $dir"
        fi

        if [ ! -w "$dir" ]; then
            log_error "目录不可写: $dir"
            return 1
        fi
    done

    return 0
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."

    local backup_usage=$(df -h "$BACKUP_BASE_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    local fra_usage=$(df -h "$FRA_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')

    if [ "$backup_usage" -gt 85 ]; then
        log_warning "备份目录空间使用率过高: ${backup_usage}%"
    else
        log_success "备份目录空间使用率正常: ${backup_usage}%"
    fi

    if [ "$fra_usage" -gt "$FRA_USAGE_THRESHOLD" ]; then
        log_warning "FRA空间使用率过高: ${fra_usage}%"
    else
        log_success "FRA空间使用率正常: ${fra_usage}%"
    fi

    return 0
}

# 主环境检查函数
main_environment_check() {
    log_info "========================================="
    log_info "开始Oracle环境检查 - 单FRA模式"
    log_info "检查时间: $(date)"
    log_info "归档模式: $ARCHIVE_MODE"
    log_info "========================================="

    local check_failed=false

    if ! check_oracle_environment; then
        check_failed=true
    fi

    if ! check_database_status; then
        check_failed=true
    fi

    if ! check_fra_configuration; then
        check_failed=true
    fi

    if ! check_backup_directories; then
        check_failed=true
    fi

    if ! check_disk_space; then
        check_failed=true
    fi

    log_info "========================================="
    if [ "$check_failed" = true ]; then
        log_error "环境检查完成，发现问题需要处理"
        return 1
    else
        log_success "环境检查完成，所有检查项通过"
        return 0
    fi
}

# 如果直接执行此脚本，则运行环境检查
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main_environment_check
fi
