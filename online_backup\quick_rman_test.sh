#!/bin/bash

# 快速RMAN测试脚本 - 避免长时间等待
# 功能：快速验证RMAN基本功能，不执行耗时操作

# 加载环境配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/0_oracle_env.sh"

# 测试日志
TEST_LOG="$BACKUP_LOG_DIR/quick_rman_test_$(date +%Y%m%d_%H%M%S).log"
mkdir -p "$BACKUP_LOG_DIR"

echo "========================================="
echo "Oracle RMAN 快速测试"
echo "测试时间: $(date)"
echo "测试日志: $TEST_LOG"
echo "========================================="

# 测试函数 - 带超时控制
run_rman_test() {
    local test_name="$1"
    local rman_commands="$2"
    local timeout_seconds="${3:-30}"  # 默认30秒超时
    
    echo "执行测试: $test_name (超时: ${timeout_seconds}秒)"
    
    # 使用timeout命令（如果可用）
    if command -v timeout >/dev/null 2>&1; then
        timeout $timeout_seconds rman target / log="$TEST_LOG" <<EOF
$rman_commands
EXIT;
EOF
    else
        # 如果没有timeout命令，直接执行
        rman target / log="$TEST_LOG" <<EOF
$rman_commands
EXIT;
EOF
    fi
    
    return $?
}

# 测试1: RMAN连接
echo "测试1: RMAN基本连接..."
if run_rman_test "基本连接" "SHOW CONFIGURATION;" 15; then
    echo "✅ 测试1通过: RMAN连接正常"
else
    echo "❌ 测试1失败: RMAN连接问题"
    exit 1
fi

# 测试2: 数据库状态检查
echo "测试2: 数据库状态检查..."
if run_rman_test "数据库状态" "REPORT SCHEMA;" 20; then
    echo "✅ 测试2通过: 数据库状态正常"
else
    echo "❌ 测试2失败: 数据库状态异常"
    exit 1
fi

# 测试3: 简单备份语法（只备份控制文件）
echo "测试3: 简单备份语法..."
TEST_BACKUP_DIR="$BACKUP_BASE_DIR/quick_test_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$TEST_BACKUP_DIR"

BACKUP_CMD="BACKUP CURRENT CONTROLFILE FORMAT '$TEST_BACKUP_DIR/quick_test_ctl_%U.bkp';"

if run_rman_test "控制文件备份" "$BACKUP_CMD" 30; then
    echo "✅ 测试3通过: 备份语法正确"
    
    # 检查备份文件
    if ls "$TEST_BACKUP_DIR"/*.bkp >/dev/null 2>&1; then
        echo "✅ 备份文件生成成功"
    else
        echo "⚠️  备份命令执行成功，但文件可能在其他位置"
    fi
else
    echo "❌ 测试3失败: 备份语法问题"
    exit 1
fi

# 测试4: 列出现有备份
echo "测试4: 列出现有备份..."
if run_rman_test "列出备份" "LIST BACKUP SUMMARY;" 20; then
    echo "✅ 测试4通过: 备份列表命令正常"
else
    echo "❌ 测试4失败: 备份列表命令问题"
    exit 1
fi

# 测试5: 检查归档日志
echo "测试5: 检查归档日志..."
if run_rman_test "归档日志检查" "LIST ARCHIVELOG SEQUENCE BETWEEN 1 AND 5;" 15; then
    echo "✅ 测试5通过: 归档日志命令正常"
else
    echo "❌ 测试5失败: 归档日志命令问题"
    exit 1
fi

echo "========================================="
echo "🎉 快速RMAN测试全部通过！"
echo ""
echo "测试结果总结："
echo "✅ RMAN连接正常"
echo "✅ 数据库状态正常" 
echo "✅ 备份语法正确"
echo "✅ 备份列表功能正常"
echo "✅ 归档日志功能正常"
echo ""
echo "现在可以安全执行完整备份："
echo "  ./2_rman_backup_level0.sh"
echo "  ./3_rman_backup_level1.sh"
echo ""
echo "详细日志: $TEST_LOG"
echo "========================================="
