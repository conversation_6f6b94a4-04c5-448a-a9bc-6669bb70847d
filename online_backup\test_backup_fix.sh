#!/bin/bash

# Oracle RMAN备份修复验证脚本 - Standard Edition兼容测试
# 功能：测试修复后的备份脚本是否能正常工作

# 加载环境配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/0_oracle_env.sh"

# 测试配置
TEST_DATE=$(date +%Y%m%d_%H%M%S)
TEST_LOGFILE="$BACKUP_LOG_DIR/backup_test_$TEST_DATE.log"

# 创建日志目录
mkdir -p "$BACKUP_LOG_DIR"

# 日志函数
log_test() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$TEST_LOGFILE"
}

log_test_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" | tee -a "$TEST_LOGFILE" >&2
}

log_test_success() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1" | tee -a "$TEST_LOGFILE"
}

# 测试RMAN连接和基本功能
test_rman_basic() {
    log_test "========================================="
    log_test "测试RMAN基本功能"
    log_test "========================================="
    
    # 测试RMAN连接
    log_test "测试RMAN连接..."
    if rman target / <<< "EXIT;" > /dev/null 2>&1; then
        log_test_success "RMAN连接正常"
    else
        log_test_error "RMAN连接失败"
        return 1
    fi
    
    # 测试基本RMAN命令
    log_test "测试RMAN基本命令..."
    rman target / log="$TEST_LOGFILE" <<EOF
SHOW ALL;
LIST BACKUP SUMMARY;
REPORT SCHEMA;
EXIT;
EOF
    
    if [ $? -eq 0 ]; then
        log_test_success "RMAN基本命令测试通过"
    else
        log_test_error "RMAN基本命令测试失败"
        return 1
    fi
    
    return 0
}

# 测试RMAN基本配置
test_rman_configuration() {
    log_test "========================================="
    log_test "测试RMAN基本配置"
    log_test "========================================="

    # 测试RMAN配置命令
    log_test "测试RMAN配置命令..."
    rman target / log="$TEST_LOGFILE" <<EOF
SHOW ALL;
CONFIGURE DEFAULT DEVICE TYPE TO DISK;
CONFIGURE BACKUP OPTIMIZATION ON;
EXIT;
EOF

    if [ $? -eq 0 ]; then
        log_test_success "RMAN配置测试通过"
    else
        log_test_error "RMAN配置测试失败"
        return 1
    fi

    return 0
}

# 测试归档日志配置
test_archivelog_config() {
    log_test "========================================="
    log_test "测试归档日志配置（单FRA模式）"
    log_test "========================================="
    
    # 检查归档模式
    local archive_mode=$(sqlplus -s / as sysdba <<EOF
SET PAGESIZE 0 FEEDBACK OFF VERIFY OFF HEADING OFF ECHO OFF
SELECT LOG_MODE FROM V\$DATABASE;
EXIT;
EOF
)
    
    if [ "$archive_mode" = "ARCHIVELOG" ]; then
        log_test_success "数据库处于归档模式: $archive_mode"
    else
        log_test_error "数据库未处于归档模式: $archive_mode"
        return 1
    fi
    
    # 检查归档目标配置
    log_test "检查归档目标配置..."
    sqlplus -s / as sysdba <<EOF >> "$TEST_LOGFILE" 2>&1
SET PAGESIZE 1000 LINESIZE 200
SELECT 'Archive Destination Check:' AS INFO FROM DUAL;
SELECT DEST_ID, DESTINATION, STATUS, BINDING FROM V\$ARCHIVE_DEST WHERE DEST_ID <= 5 AND STATUS != 'INACTIVE';
SELECT 'FRA Configuration:' AS INFO FROM DUAL;
SELECT NAME, VALUE FROM V\$PARAMETER WHERE NAME LIKE '%recovery_file_dest%';
EXIT;
EOF
    
    if [ $? -eq 0 ]; then
        log_test_success "归档配置检查完成"
    else
        log_test_error "归档配置检查失败"
        return 1
    fi
    
    return 0
}

# 测试简单备份
test_simple_backup() {
    log_test "========================================="
    log_test "测试简单备份（控制文件和参数文件）"
    log_test "========================================="

    local test_backup_dir="$BACKUP_BASE_DIR/test_simple_backup_$TEST_DATE"
    mkdir -p "$test_backup_dir"

    # 执行简单测试备份（不使用RUN块）
    rman target / log="$TEST_LOGFILE" <<EOF
# 备份控制文件
BACKUP CURRENT CONTROLFILE FORMAT '$test_backup_dir/test_controlfile_%U.bkp';

# 备份参数文件
BACKUP SPFILE FORMAT '$test_backup_dir/test_spfile_%U.bkp';

# 列出最近的备份
LIST BACKUP SUMMARY;

EXIT;
EOF

    if [ $? -eq 0 ]; then
        log_test_success "简单备份测试通过"

        # 检查备份文件是否生成
        local backup_files=$(find "$test_backup_dir" -name "*.bkp" 2>/dev/null | wc -l)
        if [ "$backup_files" -gt 0 ]; then
            log_test_success "备份文件生成成功，文件数量: $backup_files"
        else
            log_test_error "备份文件未生成"
            return 1
        fi
    else
        log_test_error "简单备份测试失败"
        return 1
    fi

    return 0
}

# 主测试函数
main() {
    log_test "========================================="
    log_test "Oracle RMAN备份修复验证测试开始"
    log_test "测试时间: $(date)"
    log_test "Oracle版本: Standard Edition兼容测试"
    log_test "测试日志: $TEST_LOGFILE"
    log_test "========================================="
    
    local test_failed=0
    
    # 执行各项测试
    if ! test_rman_basic; then
        test_failed=1
    fi

    if ! test_rman_configuration; then
        test_failed=1
    fi

    if ! test_archivelog_config; then
        test_failed=1
    fi

    if ! test_simple_backup; then
        test_failed=1
    fi
    
    # 测试结果报告
    log_test "========================================="
    if [ $test_failed -eq 0 ]; then
        log_test_success "所有测试通过！备份脚本修复成功"
        log_test "现在可以安全执行Level 0和Level 1备份"
        echo "========================================="
        echo "✅ 测试通过！备份脚本已修复"
        echo "📝 详细日志: $TEST_LOGFILE"
        echo "🚀 现在可以执行: ./2_rman_backup_level0.sh"
        echo "========================================="
    else
        log_test_error "部分测试失败，请检查配置"
        echo "========================================="
        echo "❌ 测试失败，请检查日志"
        echo "📝 详细日志: $TEST_LOGFILE"
        echo "========================================="
        exit 1
    fi
    log_test "========================================="
}

# 执行主函数
main "$@"
